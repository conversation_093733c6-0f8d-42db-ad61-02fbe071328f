"use client";

import React from "react";
import { ChevronDown } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";

export function OrganizationSelector() {
  return (
    <div className="flex items-center space-x-2">
      <div className="flex items-center space-x-2">
        <div className="w-8 h-8 bg-[#00617b] rounded-lg flex items-center justify-center">
          <span className="text-white font-semibold text-sm">P</span>
        </div>
        <div>
          <h2 className="text-sm font-semibold text-gray-900">Platyfend</h2>
          <p className="text-xs text-gray-500">Organization</p>
        </div>
      </div>
    </div>
  );
}
