"use client";

import React, { useState } from "react";
import { 
  Search,
  Plus,
  ChevronUp,
  ChevronDown,
  Settings
} from "lucide-react";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";

// Repository List Component
export function RepositoryList() {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState("10");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  const mockRepositories = [
    { name: "platyfend-app" },
  ];

  const filteredRepos = mockRepositories.filter((repo) =>
    repo.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSort = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-slate-900">Repositories</h1>
          <p className="text-slate-600 mt-1">
            List of repositories accessible to Platyfend.
          </p>
        </div>
        <Button className="bg-[#00617b] hover:bg-[#004a5c] text-white shadow-sm">
          <Plus className="w-4 h-4 mr-2" />
          Add Repositories
        </Button>
      </div>

      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-3 w-4 h-4 text-slate-400" />
        <Input
          placeholder="Repo not found? Search here..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 max-w-md rounded-lg shadow-sm border-slate-300 focus:border-[#00617b] focus:ring-[#00617b]"
        />
      </div>

      {/* Repository Table */}
      <div className="bg-white rounded-lg border border-slate-200 shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-slate-50 border-b border-slate-200">
              <tr>
                <th className="text-left py-4 px-6 font-medium text-slate-700">
                  <button
                    onClick={handleSort}
                    className="flex items-center gap-1 hover:text-slate-900 transition-colors"
                  >
                    Repository
                    {sortOrder === "asc" ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </button>
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredRepos.map((repo) => (
                <tr
                  key={repo.name}
                  className="border-b border-slate-200 hover:bg-slate-50 group relative transition-colors"
                >
                  <td className="py-4 px-6">
                    <div className="font-medium text-slate-900">{repo.name}</div>
                    <button className="absolute right-6 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-slate-200 rounded">
                      <Settings className="w-4 h-4 text-slate-500" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between px-6 py-4 border-t border-slate-200 bg-slate-50">
          <div className="flex items-center gap-2 text-sm text-slate-600">
            <span>Rows per page</span>
            <Select value={rowsPerPage} onValueChange={setRowsPerPage}>
              <SelectTrigger className="w-20 h-8 bg-white rounded-md shadow-sm border-slate-300">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-4">
            <span className="text-sm text-slate-600">
              Page 1 of 1
            </span>
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                disabled={true}
                className="h-8 w-8 p-0 bg-white shadow-sm rounded-md"
              >
                ≪
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={true}
                className="h-8 w-8 p-0 bg-white shadow-sm rounded-md"
              >
                ‹
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={true}
                className="h-8 w-8 p-0 bg-white shadow-sm rounded-md"
              >
                ›
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={true}
                className="h-8 w-8 p-0 bg-white shadow-sm rounded-md"
              >
                ≫
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Export a page component that uses the repository list
export function RepositoriesPage() {
  return <RepositoryList />;
}
