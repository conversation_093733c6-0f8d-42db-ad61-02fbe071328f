import { Toaster } from "@/src/components/ui/toaster";
import { Toaster as Sonner } from "@/src/components/ui/sonner";
import { TooltipProvider } from "@/src/components/ui/tooltip";
import { ThemeProvider } from "next-themes";
import { Providers } from "@/src/components/providers";
import { AuthProvider } from "@/src/features/auth";
import { inter, fontVariables } from "@/src/lib/fonts";
import './globals.css';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={fontVariables} suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          disableTransitionOnChange
        >
          <AuthProvider>
            <Providers>
                <TooltipProvider>
                  <Toaster />
                  <Sonner />
                  {children}
                </TooltipProvider>
            </Providers>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}